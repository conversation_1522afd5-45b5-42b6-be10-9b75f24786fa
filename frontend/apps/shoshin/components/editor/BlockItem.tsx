"use client"

import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"

interface BlockItemProps {
  id: string
  name: string
  description: string
  icon: LucideIcon
  color: string
  disabled?: boolean
}

export function BlockItem({ id, name, description, icon: Icon, color, disabled = false }: BlockItemProps) {
  const handleDragStart = (e: React.DragEvent) => {
    if (disabled) {
      e.preventDefault()
      return
    }
    e.dataTransfer.setData("application/reactflow", JSON.stringify({
      type: id,
      name,
      description
    }))
    e.dataTransfer.effectAllowed = "move"
  }

  const handleClick = () => {
    if (disabled) return

    // Dispatch a custom event to be caught by the editor component
    const event = new CustomEvent('add-block-from-sidebar', {
      detail: {
        type: id,
        name,
        description
      },
    })
    window.dispatchEvent(event)
  }

  return (
    <div
      draggable={!disabled}
      onDragStart={handleDragStart}
      onClick={handleClick}
      className={cn(
        "group flex items-center gap-3 rounded-md border bg-card p-3.5 shadow-sm transition-colors",
        disabled
          ? "cursor-not-allowed opacity-60"
          : "cursor-pointer hover:bg-neutral-50 dark:hover:bg-neutral-800/50 active:cursor-grabbing"
      )}
    >
      {/* Icon */}
      <div
        className="relative flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-md"
        style={{ backgroundColor: color }}
      >
        <Icon
          className={cn(
            "text-white transition-transform duration-200",
            !disabled && "group-hover:scale-110",
            "h-[22px] w-[22px]"
          )}
        />
      </div>

      {/* Content */}
      <div className="mb-[-2px] flex flex-col gap-1">
        <h3 className="font-medium leading-none">{name}</h3>
        <p className="text-muted-foreground text-sm leading-snug">{description}</p>
      </div>
    </div>
  )
}
