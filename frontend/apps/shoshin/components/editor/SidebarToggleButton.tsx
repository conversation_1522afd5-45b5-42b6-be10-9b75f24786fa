"use client"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useSidebarStore } from "@/stores/sidebarStore"
import { PanelRight } from "lucide-react"

export function SidebarToggleButton() {
  const { isCollapsed, expand } = useSidebarStore()

  // Only show when sidebar is collapsed
  if (!isCollapsed) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            onClick={expand}
            className="fixed left-20 bottom-[18px] z-10 flex h-9 w-9 items-center justify-center rounded-lg border bg-background text-muted-foreground hover:bg-accent/50 hover:text-foreground shadow-sm"
          >
            <PanelRight className="h-5 w-5" />
            <span className="sr-only">Open Toolbar</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="right">Open Toolbar</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
