"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";

export type SidebarTab = "Blocks" | "Tools";

interface SidebarState {
  // Secondary sidebar (left sidebar with blocks/tools)
  isCollapsed: boolean;
  activeTab: SidebarTab;
  searchQuery: string;
  
  // Actions
  toggleCollapsed: () => void;
  setActiveTab: (tab: SidebarTab) => void;
  setSearchQuery: (query: string) => void;
  collapse: () => void;
  expand: () => void;
}

export const useSidebarStore = create<SidebarState>()(
  persist(
    (set) => ({
      // Initial state
      isCollapsed: false,
      activeTab: "Blocks",
      searchQuery: "",

      // Actions
      toggleCollapsed: () => set((state) => ({ isCollapsed: !state.isCollapsed })),
      setActiveTab: (tab) => set({ activeTab: tab }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      collapse: () => set({ isCollapsed: true }),
      expand: () => set({ isCollapsed: false }),
    }),
    {
      name: "shoshin-sidebar-state",
      partialize: (state) => ({ 
        isCollapsed: state.isCollapsed,
        activeTab: state.activeTab 
      }),
    }
  )
);
