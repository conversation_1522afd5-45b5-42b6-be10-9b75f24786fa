"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";

interface WorkflowState {
  // Project metadata
  projectName: string;
  lastSaved: number | null;
  
  // Actions
  setProjectName: (name: string) => void;
  updateLastSaved: () => void;
  getTimeSinceLastSaved: () => string;
}

// Helper function to format time ago
const formatTimeAgo = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (seconds < 60) {
    return "Saved just now";
  } else if (minutes < 60) {
    return `Saved ${minutes} minute${minutes === 1 ? '' : 's'} ago`;
  } else if (hours < 24) {
    return `Saved ${hours} hour${hours === 1 ? '' : 's'} ago`;
  } else {
    return `Saved ${days} day${days === 1 ? '' : 's'} ago`;
  }
};

export const useWorkflowStore = create<WorkflowState>()(
  persist(
    (set, get) => ({
      // Initial state
      projectName: "Creative Project",
      lastSaved: Date.now() - (16 * 60 * 60 * 1000), // 16 hours ago as default
      
      // Actions
      setProjectName: (name: string) => {
        set({ projectName: name.trim() || "Untitled Project" });
      },
      
      updateLastSaved: () => {
        set({ lastSaved: Date.now() });
      },
      
      getTimeSinceLastSaved: () => {
        const { lastSaved } = get();
        if (!lastSaved) return "Never saved";
        return formatTimeAgo(lastSaved);
      },
    }),
    {
      name: "shoshin-workflow-state",
      partialize: (state) => ({
        projectName: state.projectName,
        lastSaved: state.lastSaved,
      }),
    }
  )
);
